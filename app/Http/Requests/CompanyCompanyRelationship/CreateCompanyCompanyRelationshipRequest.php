<?php

namespace App\Http\Requests\CompanyCompanyRelationship;

use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateCompanyCompanyRelationshipRequest extends FormRequest
{
    const string COMPANY_ID        = 'company_id';
    const string TARGET_COMPANY_ID = 'target_company_id';
    const string RELATIONSHIP      = 'relationship';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_USER_RELATIONSHIPS_EDIT->value);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::COMPANY_ID        => ['required', 'integer', Rule::exists(Company::class, 'id')],
            self::TARGET_COMPANY_ID => ['required', 'integer', Rule::exists(Company::class, 'id'), 'different:' . self::COMPANY_ID],
            self::RELATIONSHIP      => ['required', 'string', 'max:255'],
        ];
    }
}
