<?php

namespace App\Http\Controllers\API\CompanyCompanyRelationship;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCompanyRelationship\CreateCompanyCompanyRelationshipRequest;
use App\Http\Requests\CompanyCompanyRelationship\ListCompanyCompanyRelationshipRequest;
use App\Http\Requests\CompanyCompanyRelationship\UpdateCompanyCompanyRelationshipRequest;
use App\Http\Resources\CompanyCompanyRelationship\CompanyCompanyRelationshipResource;
use App\Models\CompanyCompanyRelationship;
use App\Repositories\CompanyCompanyRelationship\CompanyCompanyRelationshipRepository;
use App\Services\CompanyCompanyRelationship\CompanyCompanyRelationshipService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CompanyCompanyRelationshipController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyCompanyRelationshipService $companyCompanyRelationshipService,
        protected CompanyCompanyRelationshipRepository $companyCompanyRelationshipRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListCompanyCompanyRelationshipRequest $request
     * @return array
     */
    public function list(ListCompanyCompanyRelationshipRequest $request): array
    {
        $validated = $request->validated();

        $query = $this->companyCompanyRelationshipService->list(
            companyId: Arr::get($validated, ListCompanyCompanyRelationshipRequest::COMPANY_ID),
            targetCompanyId: Arr::get($validated, ListCompanyCompanyRelationshipRequest::TARGET_COMPANY_ID),
            relationship: Arr::get($validated, ListCompanyCompanyRelationshipRequest::RELATIONSHIP),
            active: Arr::get($validated, ListCompanyCompanyRelationshipRequest::ACTIVE),
        )->latest();

        return CompanyCompanyRelationshipResource::paginate($query);
    }

    /**
     * @param CreateCompanyCompanyRelationshipRequest $request
     * @return JsonResponse
     */
    public function createCompanyCompanyRelationship(CreateCompanyCompanyRelationshipRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $relationship = $this->companyCompanyRelationshipService->create(
            companyId: Arr::get($validated, CreateCompanyCompanyRelationshipRequest::COMPANY_ID),
            targetCompanyId: Arr::get($validated, CreateCompanyCompanyRelationshipRequest::TARGET_COMPANY_ID),
            relationship: Arr::get($validated, CreateCompanyCompanyRelationshipRequest::RELATIONSHIP),
            createdById: auth()->id(),
        );

        return $this->formatResponse([
            'relationship' => new CompanyCompanyRelationshipResource($relationship),
        ]);
    }

    /**
     * @param int $id
     * @return CompanyCompanyRelationshipResource
     */
    public function getCompanyCompanyRelationship(int $id): CompanyCompanyRelationshipResource
    {
        $ccr = CompanyCompanyRelationship::withTrashed()->findOrFail($id);

        return new CompanyCompanyRelationshipResource($ccr);
    }

    /**
     * @param int $id
     * @param UpdateCompanyCompanyRelationshipRequest $request
     * @return JsonResponse
     */
    public function updateCompanyCompanyRelationship(int $id, UpdateCompanyCompanyRelationshipRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $ccr = CompanyCompanyRelationship::withTrashed()->findOrFail($id);

        return $this->formatResponse([
            'status' => $this->companyCompanyRelationshipService->update(
                companyCompanyRelationship: $ccr,
                relationship: Arr::get($validated, UpdateCompanyCompanyRelationshipRequest::RELATIONSHIP),
            ),
        ]);
    }
}
